import { literal, WhereOptions } from 'sequelize';
import dayjs from 'dayjs';
// import { snakeCase } from 'lodash';

import advancedFormat from 'dayjs/plugin/advancedFormat';

import { FilterAwardPayments, FilterAwards } from '../controllers/types/award.type';

import db from '../models';

const { Employee: User } = db;

const { Op } = require('sequelize');
const strings = require('../config/strings');

dayjs.extend(advancedFormat);

const {
  awards: { statusStrings },
} = strings;

const NO_ID = '-1';
const filterConstruct = (list: string[]) => {
  let filterConstr: WhereOptions = { [Op.in]: list };
  const noIdInd = list.indexOf(NO_ID);

  if (noIdInd >= 0) {
    list.splice(noIdInd, 1);

    filterConstr = {
      [Op.or]: {
        [Op.ne]: null,
        [Op.or]: {
          [Op.in]: list,
        },
      },
    };
  }

  return filterConstr;
};

export const getFilterCriteria = async ({
  funders,
  departments,
  programs,
  status,
  assignees,
  dateFrom: startDate,
  dateTo: endDate,
  endDateType: dateType = 'endsOn',
  sortBy,
  sortOrder = 'desc',
  assigned,
}: FilterAwards) => {
  const endDateType = dateType === 'End Date' ? 'endsOn' : dateType;

  const queryFilters: WhereOptions = {};

  if (funders) {
    const splitFunders = funders.split(/(?<!,),(?!,)/g).map((e) => e.replace(/,,/g, ',').trim());

    queryFilters.funder = { [Op.iLike]: `%${splitFunders[0]}%` };

    const ors: WhereOptions = [];

    // eslint-disable-next-line guard-for-in
    for (const index in splitFunders) {
      const funder = splitFunders[index];

      ors.push({ [Op.iLike]: `%${funder}%` });
    }

    queryFilters.funder[Op.or] = ors;
  }

  if (departments) {
    const splitDepartments = departments
      .split(/(?<!,),(?!,)/g)
      .map((e) => e.replace(/,,/g, ',').trim());

    queryFilters.department = { [Op.iLike]: `%${splitDepartments[0]}%` };

    const ors: WhereOptions = [];

    // eslint-disable-next-line guard-for-in
    for (const index in splitDepartments) {
      const department = splitDepartments[index];

      ors.push({ [Op.iLike]: `%${department}%` });
    }

    queryFilters.department[Op.or] = ors;
  }

  if (programs) {
    const splitPrograms = programs.split(/(?<!,),(?!,)/g).map((e) => e.replace(/,,/g, ',').trim());

    queryFilters.grantProgramName = { [Op.iLike]: `%${splitPrograms[0]}%` };

    const ors: WhereOptions = [];

    // eslint-disable-next-line guard-for-in
    for (const index in splitPrograms) {
      const program = splitPrograms[index];

      ors.push({ [Op.iLike]: `%${program}%` });
    }

    queryFilters.grantProgramName[Op.or] = ors;
  }

  if (assignees) {
    const users = await User.findAll({
      where: {
        [Op.or]: assignees.split(',').map((name) => ({
          name: { [Op.iLike]: `%${name.trim()}%` }, // Adjust wildcard placement as needed
        })),
      },
    });

    queryFilters.assigneeId = filterConstruct((users || []).map((u: typeof User) => u?.id || '-1'));
  }
  if (startDate && endDate) {
    // If dateFrom and dateTo are the same, set the time to 00:00:00
    const dateFrom = startDate === endDate ? new Date(startDate).setHours(0, 0, 0, 0) : startDate;
    const dateTo = startDate === endDate ? new Date(endDate).setHours(23, 59, 59, 999) : endDate;

    queryFilters[Op.and] = [{ [endDateType]: { [Op.between]: [dateFrom, dateTo] } }];
  }
  if (startDate && !endDate) {
    const dateFrom = new Date(startDate).setHours(0, 0, 0, 0);
    const dateTo = new Date(startDate).setHours(23, 59, 59, 999);

    queryFilters[Op.and] = [{ [endDateType]: { [Op.between]: [dateFrom, dateTo] } }];
  }

  // const statuses = Object.keys(statusStrings);

  if (status) {
    queryFilters.status = { [Op.eq]: statusStrings[status] };
  }

  let sortingQuery;

  switch (sortBy) {
    case 'assignee':
      sortingQuery = [['assignee', 'name', sortOrder]];
      break;
    case 'client':
      sortingQuery = [literal(`"client"."name" ${sortOrder}`)];
      break;
    case 'program':
      sortingQuery = [['program', 'name', sortOrder]];
      break;
    case 'nextReportDueDate':
      sortingQuery = [literal(`"nextReportDueDate" ${sortOrder}`)];
      break;
    default:
      sortingQuery = [[sortBy, sortOrder]];
      break;
  }
  if (assigned) {
    if (assigned === 'assignee') {
      queryFilters.assigneeId = { [Op.ne]: null }; // Only assigned awards
    } else if (assigned === 'unassignee') {
      queryFilters.assigneeId = { [Op.is]: null }; // Only unassigned awards
    }
  }

  console.log('queryFilters:', queryFilters);
  console.log('sortingQuery:', sortingQuery);

  return { queryFilters, sortingQuery, status };
};

export const getPaymentFilterCriteria = ({ type, users, awards }: FilterAwardPayments) => {
  const queryFilters: WhereOptions = {};

  if (awards) queryFilters.awardId = filterConstruct(awards.split(','));
  if (users) queryFilters.userId = filterConstruct(users.split(','));

  if (type) queryFilters.type = type;

  return { queryFilters };
};
